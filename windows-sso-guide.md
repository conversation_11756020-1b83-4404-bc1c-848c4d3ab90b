# Windows SSO Integration Guide

## Overview

This guide details how to implement Single Sign-On (SSO) that shares Windows domain login credentials with FIDO2/OIDC authentication, providing seamless user experience while maintaining strong security.

## SSO Architecture

### Complete SSO Flow

```mermaid
sequenceDiagram
    participant U as User
    participant W as Windows
    participant B as Browser
    participant IIS as IIS/Web Server
    participant ADFS as AD FS
    participant APP as Application
    participant FIDO as FIDO2 Device
    
    Note over U,W: Morning Login
    U->>W: Windows Login (Password/Biometric)
    W->>W: Generate Kerberos TGT
    W->>W: Store in LSA cache
    
    Note over U,APP: First Application Access
    U->>B: Open browser, navigate to app
    B->>APP: GET /protected-resource
    APP->>B: 302 Redirect to ADFS
    
    Note over B,ADFS: Seamless Authentication
    B->>ADFS: GET /auth (automatic Kerberos)
    ADFS->>ADFS: Validate Kerberos ticket
    ADFS->>ADFS: Extract user identity & groups
    
    alt High-Security Resource
        ADFS->>B: Require FIDO2 step-up
        B->>U: Show "Touch your security key"
        U->>FIDO: Touch/PIN/Biometric
        FIDO->>B: Signed assertion
        B->>ADFS: Submit FIDO2 proof
        ADFS->>ADFS: Verify signature
    else Standard Resource
        Note over ADFS: Skip additional auth
    end
    
    ADFS->>B: Return OIDC authorization code
    B->>APP: GET /callback?code=...
    APP->>ADFS: Exchange code for tokens
    ADFS->>APP: ID token + Access token
    APP->>B: Set session cookie
    B->>U: Show application content
    
    Note over U,APP: Subsequent Access (Same Day)
    U->>B: Navigate to another app
    B->>APP: GET /other-app
    Note over B,APP: Uses existing SSO session
    APP->>B: Immediate access granted
```

## Implementation Steps

### Step 1: Domain Controller Configuration

#### Enable Kerberos Delegation
```powershell
# Configure constrained delegation for ADFS service account
$adfsAccount = "DOMAIN\ADFS-Service"
$targetSPN = "HTTP/adfs.yourdomain.com"

# Set up constrained delegation
Set-ADUser -Identity $adfsAccount -Add @{
    'msDS-AllowedToDelegateTo' = $targetSPN
}

# Enable protocol transition
Set-ADUser -Identity $adfsAccount -TrustedToAuthForDelegation $true

# Configure SPNs
setspn -A HTTP/adfs.yourdomain.com $adfsAccount
setspn -A HTTP/adfs $adfsAccount
```

#### Group Policy for SSO
```xml
<!-- Group Policy: Computer Configuration > Administrative Templates > Windows Components > Internet Explorer -->
<GroupPolicySettings>
  <LocalIntranetSites>
    <Site>https://*.yourdomain.com</Site>
    <Site>https://adfs.yourdomain.com</Site>
  </LocalIntranetSites>
  
  <SecuritySettings Zone="LocalIntranet">
    <AutomaticLogonWithCurrentUserName>Enable</AutomaticLogonWithCurrentUserName>
    <AutomaticLogonOnlyInIntranetZone>Enable</AutomaticLogonOnlyInIntranetZone>
  </SecuritySettings>
</GroupPolicySettings>
```

### Step 2: AD FS Configuration

#### Install and Configure AD FS
```powershell
# Install AD FS role
Install-WindowsFeature -Name ADFS-Federation -IncludeManagementTools

# Configure AD FS farm
$credential = Get-Credential -Message "Enter ADFS Service Account credentials"
$certificateThumbprint = "YOUR_SSL_CERTIFICATE_THUMBPRINT"

Install-AdfsFarm `
    -CertificateThumbprint $certificateThumbprint `
    -FederationServiceName "adfs.yourdomain.com" `
    -ServiceAccountCredential $credential `
    -OverwriteConfiguration

# Enable Windows Integrated Authentication
Set-AdfsGlobalAuthenticationPolicy `
    -PrimaryIntranetAuthenticationProvider @('WindowsAuthentication', 'FormsAuthentication') `
    -PrimaryExtranetAuthenticationProvider @('FormsAuthentication') `
    -WindowsIntegratedFallbackEnabled $true
```

#### Configure OIDC Application
```powershell
# Add OIDC Relying Party
Add-AdfsClient `
    -ClientId "your-app-client-id" `
    -Name "Your Application" `
    -RedirectUri @("https://yourapp.com/auth/callback") `
    -Description "SSO-enabled application with FIDO2"

# Configure claims for the application
$ruleSet = @"
@RuleName = "Issue UPN as Name ID"
c:[Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/upn"]
=> issue(Type = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier", Value = c.Value);

@RuleName = "Issue Groups"
c:[Type == "http://schemas.microsoft.com/ws/2008/06/identity/claims/groupsid", Issuer == "AD AUTHORITY"]
=> issue(store = "Active Directory", types = ("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/role"), query = ";tokenGroups;{0}", param = c.Value);

@RuleName = "Issue Authentication Methods"
c:[Type == "http://schemas.microsoft.com/claims/authnmethodsreferences"]
=> issue(Type = "amr", Value = c.Value);
"@

Set-AdfsRelyingPartyTrust `
    -TargetIdentifier "your-app-client-id" `
    -IssuanceTransformRules $ruleSet
```

### Step 3: FIDO2 Step-up Authentication

#### Configure Conditional FIDO2
```csharp
// C# example for conditional FIDO2 in ADFS custom authentication
public class ConditionalFIDO2Adapter : IAuthenticationAdapter
{
    public IAuthenticationAdapterMetadata Metadata { get; }
    
    public IAdapterPresentation BeginAuthentication(
        Claim identityClaim, 
        HttpListenerRequest request, 
        IAuthenticationContext context)
    {
        var userPrincipal = identityClaim.Value;
        var userGroups = GetUserGroups(userPrincipal);
        
        // Check if user requires FIDO2 step-up
        var requiresFIDO2 = userGroups.Any(g => 
            g.Equals("Domain Admins", StringComparison.OrdinalIgnoreCase) ||
            g.Equals("Finance", StringComparison.OrdinalIgnoreCase) ||
            g.Equals("Executives", StringComparison.OrdinalIgnoreCase));
        
        if (requiresFIDO2)
        {
            // Generate FIDO2 challenge
            var challenge = GenerateFIDO2Challenge(userPrincipal);
            context.Data.Add("fido2_challenge", challenge);
            
            return new FIDO2Presentation(challenge, userPrincipal);
        }
        
        // Skip FIDO2 for regular users
        return new SuccessPresentation();
    }
    
    public IAdapterPresentation TryEndAuthentication(
        IAuthenticationContext context, 
        IProofData proofData, 
        HttpListenerRequest request, 
        out Claim[] claims)
    {
        if (context.Data.ContainsKey("fido2_challenge"))
        {
            var assertion = proofData.Properties["fido2_assertion"];
            var isValid = ValidateFIDO2Assertion(assertion, context.Data["fido2_challenge"]);
            
            if (isValid)
            {
                claims = new[]
                {
                    new Claim("http://schemas.microsoft.com/claims/authnmethodsreferences", "fido"),
                    new Claim("http://custom/claims/stepup_completed", "true")
                };
                return new SuccessPresentation();
            }
            
            claims = null;
            return new ErrorPresentation("FIDO2 authentication failed");
        }
        
        claims = new[]
        {
            new Claim("http://schemas.microsoft.com/claims/authnmethodsreferences", "kerberos")
        };
        return new SuccessPresentation();
    }
}
```

### Step 4: Client Application Integration

#### Detect SSO Status
```javascript
// Client-side SSO detection and handling
class SSOManager {
  constructor(oidcConfig) {
    this.oidcConfig = oidcConfig;
    this.userManager = new Oidc.UserManager(oidcConfig);
  }

  async checkSSOStatus() {
    try {
      // Try silent authentication first
      const user = await this.userManager.signinSilent();
      
      if (user && !user.expired) {
        return {
          authenticated: true,
          user: user,
          requiresStepup: this.checkStepupRequired(user)
        };
      }
    } catch (error) {
      console.log('Silent SSO failed, will redirect to login');
    }
    
    return { authenticated: false };
  }

  checkStepupRequired(user) {
    // Check if user has completed step-up authentication
    const amr = user.profile.amr || [];
    const stepupCompleted = user.profile.stepup_completed === 'true';
    
    // Check if user is in high-privilege groups
    const roles = user.profile.role || [];
    const highPrivilegeRoles = ['Domain Admins', 'Finance', 'Executives'];
    const isHighPrivilege = roles.some(role => 
      highPrivilegeRoles.includes(role));
    
    return isHighPrivilege && (!amr.includes('fido') || !stepupCompleted);
  }

  async performStepupAuthentication() {
    // Redirect to ADFS with step-up parameter
    const stepupUrl = `${this.oidcConfig.authority}/auth?` +
      `client_id=${this.oidcConfig.client_id}&` +
      `response_type=code&` +
      `scope=${this.oidcConfig.scope}&` +
      `redirect_uri=${this.oidcConfig.redirect_uri}&` +
      `prompt=login&` +
      `acr_values=urn:mace:incommon:iap:gold`;
    
    window.location.href = stepupUrl;
  }

  async initializeSSO() {
    const status = await this.checkSSOStatus();
    
    if (status.authenticated) {
      if (status.requiresStepup) {
        // Show step-up authentication prompt
        this.showStepupPrompt();
      } else {
        // User is fully authenticated
        this.onAuthenticationComplete(status.user);
      }
    } else {
      // Redirect to login
      await this.userManager.signinRedirect();
    }
  }

  showStepupPrompt() {
    const modal = document.createElement('div');
    modal.innerHTML = `
      <div class="stepup-modal">
        <h3>Additional Security Required</h3>
        <p>Please touch your security key to continue.</p>
        <button onclick="ssoManager.performStepupAuthentication()">
          Continue with Security Key
        </button>
      </div>
    `;
    document.body.appendChild(modal);
  }
}

// Initialize SSO on page load
document.addEventListener('DOMContentLoaded', () => {
  const oidcConfig = {
    authority: 'https://adfs.yourdomain.com',
    client_id: 'your-app-client-id',
    redirect_uri: window.location.origin + '/auth/callback',
    response_type: 'code',
    scope: 'openid profile email',
    automaticSilentRenew: true,
    silent_redirect_uri: window.location.origin + '/auth/silent-callback'
  };

  window.ssoManager = new SSOManager(oidcConfig);
  ssoManager.initializeSSO();
});
```

## SSO Session Management

### Session Lifecycle

```mermaid
stateDiagram-v2
    [*] --> WindowsLogin: User logs into Windows
    WindowsLogin --> KerberosTicket: TGT generated
    KerberosTicket --> BrowserAccess: User opens browser
    BrowserAccess --> SilentAuth: Automatic Kerberos auth
    SilentAuth --> BasicSSO: Standard user
    SilentAuth --> StepupRequired: Privileged user
    StepupRequired --> FIDO2Challenge: Show security key prompt
    FIDO2Challenge --> StepupComplete: User touches key
    StepupComplete --> EnhancedSSO: Full access granted
    BasicSSO --> SessionActive: Limited access
    EnhancedSSO --> SessionActive: Full access
    SessionActive --> SessionRenewal: Token refresh
    SessionRenewal --> SessionActive: Continue session
    SessionActive --> WindowsLogout: User logs out
    WindowsLogout --> [*]: Session terminated
```

### Benefits of Windows SSO + FIDO2

1. **Seamless User Experience**
   - Single login for entire work day
   - No repeated password prompts
   - Automatic authentication for trusted applications

2. **Enhanced Security**
   - Kerberos provides strong initial authentication
   - FIDO2 adds phishing-resistant step-up for sensitive operations
   - Hardware-bound credentials prevent credential theft

3. **Administrative Benefits**
   - Centralized user management through Active Directory
   - Granular access control based on group membership
   - Comprehensive audit logging across all systems

4. **Compliance Support**
   - Multi-factor authentication for privileged access
   - Non-repudiation through hardware-bound signatures
   - Detailed authentication logs for compliance reporting

This SSO implementation provides the best of both worlds: the convenience of seamless Windows authentication with the security of FIDO2 hardware keys for sensitive operations.
