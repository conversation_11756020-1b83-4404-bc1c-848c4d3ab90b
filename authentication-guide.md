# Domain Authentication with OpenID Connect and FIDO2

## Overview

This document describes the implementation of secure domain authentication using OpenID Connect (OIDC) protocol combined with FIDO2/WebAuthn for hardware-based authentication. This approach provides strong, phishing-resistant authentication using compatible hardware devices.

## Architecture Components

### Core Components
- **Identity Provider (IdP)**: OpenID Connect compliant server
- **Relying Party (RP)**: Your application/domain
- **FIDO2 Authenticator**: Hardware security key or platform authenticator
- **User Agent**: Web browser with WebAuthn support

### Key Technologies
- **OpenID Connect**: OAuth 2.0-based identity layer
- **FIDO2/WebAuthn**: W3C standard for strong authentication
- **CTAP2**: Client-to-Authenticator Protocol
- **Hardware Security Keys**: USB, NFC, or Bluetooth devices

## Authentication Flow Overview

```mermaid
flowchart TD
    A[User Access Request] --> B{Domain Authentication Required?}
    B -->|Yes| C[Redirect to OIDC Provider]
    B -->|No| D[Direct Access]

    C --> E[OIDC Authentication Flow]
    E --> F{FIDO2 Required?}
    F -->|Yes| G[FIDO2 Challenge]
    F -->|No| H[Standard OIDC Flow]

    G --> I[Hardware Device Interaction]
    I --> J{Authentication Success?}
    J -->|Yes| K[Generate ID Token]
    J -->|No| L[Authentication Failed]

    H --> M[Username/Password]
    M --> N{Credentials Valid?}
    N -->|Yes| K
    N -->|No| L

    K --> O[Return to Application]
    O --> P[Access Granted]
    L --> Q[Access Denied]
```

## OpenID Connect Authentication Sequence

```mermaid
sequenceDiagram
    participant U as User
    participant B as Browser
    participant RP as Relying Party
    participant IdP as OIDC Provider
    participant HS as Hardware Security Key

    U->>B: Access protected resource
    B->>RP: GET /protected-resource
    RP->>B: 302 Redirect to IdP
    Note over RP,B: Authorization Request with PKCE

    B->>IdP: GET /auth?response_type=code&client_id=...
    IdP->>B: Present login form

    alt FIDO2 Authentication
        B->>IdP: Request FIDO2 challenge
        IdP->>B: Return challenge options
        B->>HS: navigator.credentials.get()
        HS->>U: User presence/verification
        U->>HS: Touch/PIN/Biometric
        HS->>B: Signed assertion
        B->>IdP: Submit assertion
    else Standard Authentication
        U->>B: Enter credentials
        B->>IdP: Submit credentials
    end

    IdP->>B: 302 Redirect with authorization code
    B->>RP: GET /callback?code=...
    RP->>IdP: POST /token (exchange code)
    IdP->>RP: Return ID token + access token
    RP->>B: Set session, redirect to resource
    B->>U: Display protected resource
```

## FIDO2/WebAuthn Registration Flow

```mermaid
sequenceDiagram
    participant U as User
    participant B as Browser
    participant RP as Relying Party
    participant IdP as OIDC Provider
    participant HS as Hardware Security Key

    U->>B: Initiate FIDO2 registration
    B->>RP: Request registration
    RP->>IdP: Forward registration request

    IdP->>IdP: Generate challenge
    IdP->>B: Return PublicKeyCredentialCreationOptions
    Note over IdP,B: Challenge, user info, RP info, algorithms

    B->>HS: navigator.credentials.create()
    HS->>U: User verification required
    U->>HS: Touch + PIN/Biometric

    HS->>HS: Generate key pair
    HS->>HS: Create attestation
    HS->>B: Return PublicKeyCredential

    B->>IdP: Submit credential
    IdP->>IdP: Verify attestation
    IdP->>IdP: Store public key
    IdP->>B: Registration success
    B->>U: Display success message
```

## Domain Authentication Flow

```mermaid
flowchart TD
    subgraph Domain["Corporate Domain"]
        A[User Workstation] --> B[Domain Controller]
        B --> C[Certificate Authority]
        C --> D[OIDC Provider]
    end

    subgraph Process["Authentication Process"]
        E[Domain Login] --> F[Certificate-based Auth]
        F --> G[OIDC Token Request]
        G --> H[FIDO2 Challenge]
        H --> I[Hardware Key Verification]
        I --> J[Access Token Issued]
    end

    A --> E
    J --> K[Application Access]
```

## Hardware Device Interaction

```mermaid
sequenceDiagram
    participant App as Application
    participant Browser as Browser
    participant WebAuthn as WebAuthn API
    participant CTAP as CTAP2 Protocol
    participant Device as Hardware Key

    App->>Browser: Request authentication
    Browser->>WebAuthn: navigator.credentials.get()
    WebAuthn->>CTAP: authenticatorGetAssertion

    CTAP->>Device: Send challenge
    Device->>Device: Verify user presence

    alt PIN Required
        Device->>CTAP: Request PIN
        CTAP->>Browser: PIN prompt
        Browser->>App: Show PIN dialog
        App->>Browser: User enters PIN
        Browser->>CTAP: Send PIN
        CTAP->>Device: Verify PIN
    end

    alt Biometric Available
        Device->>Device: Biometric verification
    else Touch Required
        Device->>Device: Wait for touch
    end

    Device->>Device: Sign challenge
    Device->>CTAP: Return signature
    CTAP->>WebAuthn: Assertion response
    WebAuthn->>Browser: PublicKeyCredential
    Browser->>App: Authentication result
```

## Security Considerations

### FIDO2 Security Features
- **Phishing Resistance**: Origin binding prevents credential replay
- **Privacy**: No trackable identifiers across sites
- **Attestation**: Hardware authenticity verification
- **User Verification**: PIN, biometric, or presence confirmation

### OpenID Connect Security
- **PKCE**: Proof Key for Code Exchange prevents authorization code interception
- **State Parameter**: CSRF protection
- **Nonce**: Replay attack prevention
- **Token Validation**: Signature and claims verification

### Domain Integration Security
- **Certificate-based Authentication**: PKI infrastructure
- **Mutual TLS**: Client certificate verification
- **Token Binding**: Cryptographic binding of tokens to TLS connections
- **Audience Restriction**: Tokens valid only for specific domains

## Hardware Device Compatibility

### Supported FIDO2 Devices

#### USB Security Keys
- **YubiKey 5 Series**: USB-A, USB-C, NFC variants
- **SoloKeys**: Open-source FIDO2 keys
- **Google Titan Security Keys**: USB and Bluetooth variants
- **Feitian ePass FIDO2**: Various form factors

#### Platform Authenticators
- **Windows Hello**: Biometric and PIN-based
- **Touch ID/Face ID**: macOS and iOS devices
- **Android Biometric**: Fingerprint and face recognition
- **Chrome OS**: Built-in security chip

#### NFC and Bluetooth Keys
- **YubiKey 5 NFC**: Near-field communication
- **Google Titan Bluetooth**: Wireless connectivity
- **Feitian MultiPass**: Multi-protocol support

### Device Requirements
- **FIDO2/WebAuthn Compliance**: CTAP2 protocol support
- **User Verification**: PIN, biometric, or presence detection
- **Attestation**: Device authenticity verification
- **Cross-platform Support**: Works across different operating systems

## Implementation Guidelines

### OIDC Provider Configuration

```yaml
# Example OIDC Provider Settings
issuer: "https://auth.yourdomain.com"
authorization_endpoint: "https://auth.yourdomain.com/auth"
token_endpoint: "https://auth.yourdomain.com/token"
userinfo_endpoint: "https://auth.yourdomain.com/userinfo"
jwks_uri: "https://auth.yourdomain.com/.well-known/jwks.json"

# FIDO2 Extensions
webauthn_endpoint: "https://auth.yourdomain.com/webauthn"
fido2_enabled: true
require_user_verification: true
```

### Client Application Setup

```javascript
// OIDC Client Configuration
const oidcConfig = {
  authority: 'https://auth.yourdomain.com',
  client_id: 'your-client-id',
  redirect_uri: 'https://app.yourdomain.com/callback',
  response_type: 'code',
  scope: 'openid profile email',
  post_logout_redirect_uri: 'https://app.yourdomain.com',

  // PKCE Configuration
  code_challenge_method: 'S256',

  // FIDO2 Integration
  extraQueryParams: {
    fido2_required: 'true'
  }
};

// WebAuthn Registration
async function registerFIDO2() {
  const options = await fetch('/webauthn/register/begin', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username: user.email })
  }).then(r => r.json());

  const credential = await navigator.credentials.create({
    publicKey: options
  });

  await fetch('/webauthn/register/complete', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(credential)
  });
}
```

### Domain Integration Steps

1. **Configure Domain Controller**
   - Enable certificate-based authentication
   - Configure OIDC provider integration
   - Set up user attribute mapping

2. **Deploy FIDO2-enabled OIDC Provider**
   - Install WebAuthn library
   - Configure FIDO2 endpoints
   - Set up attestation verification

3. **Client Application Integration**
   - Implement OIDC client
   - Add WebAuthn support
   - Handle authentication flows

4. **User Enrollment Process**
   - Domain user registration
   - FIDO2 device registration
   - Backup authentication methods

## Troubleshooting Guide

### Common Issues

#### FIDO2 Device Not Recognized
- **Symptoms**: Device not detected by browser
- **Solutions**:
  - Check USB connection
  - Verify browser WebAuthn support
  - Update device firmware
  - Try different USB port

#### Authentication Timeout
- **Symptoms**: Challenge expires before completion
- **Solutions**:
  - Increase timeout values
  - Check network connectivity
  - Verify device responsiveness
  - Clear browser cache

#### Domain Authentication Failures
- **Symptoms**: Certificate validation errors
- **Solutions**:
  - Verify certificate chain
  - Check time synchronization
  - Validate certificate revocation
  - Review domain trust relationships

### Browser Compatibility

| Browser | WebAuthn Support | Platform Authenticator | USB Keys | NFC | Bluetooth |
|---------|------------------|----------------------|----------|-----|-----------|
| Chrome 67+ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Firefox 60+ | ✅ | ✅ | ✅ | ❌ | ❌ |
| Safari 14+ | ✅ | ✅ | ✅ | ❌ | ❌ |
| Edge 18+ | ✅ | ✅ | ✅ | ✅ | ✅ |

## Best Practices

### Security Best Practices
1. **Always use HTTPS** for all authentication endpoints
2. **Implement proper CORS** policies for cross-origin requests
3. **Validate all tokens** including signature and claims verification
4. **Use short-lived tokens** with appropriate refresh mechanisms
5. **Implement rate limiting** on authentication endpoints
6. **Log security events** for monitoring and auditing

### User Experience Best Practices
1. **Provide clear instructions** for device registration
2. **Offer fallback methods** when FIDO2 is unavailable
3. **Show progress indicators** during authentication
4. **Handle errors gracefully** with user-friendly messages
5. **Support multiple devices** per user account

### Deployment Best Practices
1. **Test thoroughly** across different browsers and devices
2. **Monitor authentication metrics** and success rates
3. **Plan for device replacement** and recovery scenarios
4. **Keep libraries updated** for security patches
5. **Document configuration** for maintenance teams

## Conclusion

This authentication architecture provides enterprise-grade security by combining the identity federation capabilities of OpenID Connect with the phishing-resistant properties of FIDO2 hardware authentication. The domain integration ensures seamless user experience while maintaining strong security posture across the organization.
