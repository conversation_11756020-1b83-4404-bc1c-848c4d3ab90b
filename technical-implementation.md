# Technical Implementation Details

## OIDC Provider Configuration

### JWT Token Structure

```json
{
  "header": {
    "alg": "RS256",
    "typ": "JWT",
    "kid": "key-id-1"
  },
  "payload": {
    "iss": "https://auth.yourdomain.com",
    "sub": "user-unique-identifier",
    "aud": "your-client-id",
    "exp": **********,
    "iat": **********,
    "nonce": "random-nonce-value",
    "auth_time": **********,
    "amr": ["fido", "pwd"],
    "acr": "urn:mace:incommon:iap:silver",
    "fido2_credential_id": "base64-encoded-credential-id",
    "domain_user": "<EMAIL>",
    "groups": ["domain-users", "app-users"]
  }
}
```

### WebAuthn Server Configuration

```python
# Python example using py_webauthn
from webauthn import generate_registration_options, verify_registration_response
from webauthn import generate_authentication_options, verify_authentication_response

# Registration endpoint
@app.route('/webauthn/register/begin', methods=['POST'])
def webauthn_register_begin():
    user_id = request.json['user_id']
    username = request.json['username']
    
    options = generate_registration_options(
        rp_id="yourdomain.com",
        rp_name="Your Organization",
        user_id=user_id.encode(),
        user_name=username,
        user_display_name=username,
        attestation="direct",
        authenticator_selection=AuthenticatorSelectionCriteria(
            authenticator_attachment="cross-platform",
            user_verification="required"
        ),
        supported_pub_key_algs=[
            COSEAlgorithmIdentifier.ECDSA_SHA_256,
            COSEAlgorithmIdentifier.RSASSA_PKCS1_v1_5_SHA_256,
        ]
    )
    
    # Store challenge in session
    session['challenge'] = options.challenge
    return jsonify(options)

# Authentication endpoint
@app.route('/webauthn/authenticate/begin', methods=['POST'])
def webauthn_authenticate_begin():
    user_id = request.json['user_id']
    
    # Get user's registered credentials
    credentials = get_user_credentials(user_id)
    
    options = generate_authentication_options(
        rp_id="yourdomain.com",
        allow_credentials=[
            PublicKeyCredentialDescriptor(id=cred.credential_id)
            for cred in credentials
        ],
        user_verification="required"
    )
    
    session['challenge'] = options.challenge
    return jsonify(options)
```

## Domain Controller Integration

### Active Directory Configuration

```powershell
# Enable certificate authentication
Set-ADUser -Identity "username" -Certificates @{Add="certificate-thumbprint"}

# Configure OIDC claims mapping
New-ADClaimType -DisplayName "FIDO2CredentialID" -SourceAttribute "extensionAttribute1"
New-ADClaimTransformPolicy -Name "FIDO2Claims" -Rule @"
c:[Type == "http://schemas.microsoft.com/ws/2008/06/identity/claims/windowsaccountname"]
=> issue(Type = "fido2_credential_id", Value = c.Value);
"@
```

### Certificate Template Configuration

```xml
<!-- Certificate Template for FIDO2 Integration -->
<CertificateTemplate>
  <Name>FIDO2UserAuthentication</Name>
  <DisplayName>FIDO2 User Authentication</DisplayName>
  <KeyUsage>DigitalSignature, KeyEncipherment</KeyUsage>
  <ExtendedKeyUsage>
    <EKU>*******.*******.2</EKU> <!-- Client Authentication -->
    <EKU>*******.4.1.311.20.2.2</EKU> <!-- Smart Card Logon -->
  </ExtendedKeyUsage>
  <SubjectNameFormat>CN={UserPrincipalName}</SubjectNameFormat>
  <ValidityPeriod>2 years</ValidityPeriod>
</CertificateTemplate>
```

## Client-Side Implementation

### JavaScript WebAuthn Integration

```javascript
class FIDO2AuthManager {
  constructor(rpId, apiBase) {
    this.rpId = rpId;
    this.apiBase = apiBase;
  }

  async register(username) {
    try {
      // Get registration options from server
      const response = await fetch(`${this.apiBase}/webauthn/register/begin`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username })
      });
      
      const options = await response.json();
      
      // Convert base64url to ArrayBuffer
      options.challenge = this.base64urlToBuffer(options.challenge);
      options.user.id = this.base64urlToBuffer(options.user.id);
      
      // Create credential
      const credential = await navigator.credentials.create({
        publicKey: options
      });
      
      // Send credential to server
      await fetch(`${this.apiBase}/webauthn/register/complete`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: credential.id,
          rawId: this.bufferToBase64url(credential.rawId),
          response: {
            attestationObject: this.bufferToBase64url(credential.response.attestationObject),
            clientDataJSON: this.bufferToBase64url(credential.response.clientDataJSON)
          },
          type: credential.type
        })
      });
      
      return { success: true };
    } catch (error) {
      console.error('Registration failed:', error);
      return { success: false, error: error.message };
    }
  }

  async authenticate(username) {
    try {
      // Get authentication options
      const response = await fetch(`${this.apiBase}/webauthn/authenticate/begin`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username })
      });
      
      const options = await response.json();
      
      // Convert challenge and credential IDs
      options.challenge = this.base64urlToBuffer(options.challenge);
      options.allowCredentials = options.allowCredentials.map(cred => ({
        ...cred,
        id: this.base64urlToBuffer(cred.id)
      }));
      
      // Get assertion
      const assertion = await navigator.credentials.get({
        publicKey: options
      });
      
      // Send assertion to server
      const verifyResponse = await fetch(`${this.apiBase}/webauthn/authenticate/complete`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: assertion.id,
          rawId: this.bufferToBase64url(assertion.rawId),
          response: {
            authenticatorData: this.bufferToBase64url(assertion.response.authenticatorData),
            clientDataJSON: this.bufferToBase64url(assertion.response.clientDataJSON),
            signature: this.bufferToBase64url(assertion.response.signature),
            userHandle: assertion.response.userHandle ? 
              this.bufferToBase64url(assertion.response.userHandle) : null
          },
          type: assertion.type
        })
      });
      
      const result = await verifyResponse.json();
      return result;
    } catch (error) {
      console.error('Authentication failed:', error);
      return { success: false, error: error.message };
    }
  }

  base64urlToBuffer(base64url) {
    const base64 = base64url.replace(/-/g, '+').replace(/_/g, '/');
    const padded = base64.padEnd(base64.length + (4 - base64.length % 4) % 4, '=');
    return Uint8Array.from(atob(padded), c => c.charCodeAt(0));
  }

  bufferToBase64url(buffer) {
    const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)));
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  }
}

// Usage example
const authManager = new FIDO2AuthManager('yourdomain.com', '/api');

// Register new device
document.getElementById('register-btn').addEventListener('click', async () => {
  const result = await authManager.register(currentUser.username);
  if (result.success) {
    showMessage('Device registered successfully!');
  } else {
    showError('Registration failed: ' + result.error);
  }
});

// Authenticate with device
document.getElementById('auth-btn').addEventListener('click', async () => {
  const result = await authManager.authenticate(currentUser.username);
  if (result.success) {
    // Redirect to application or update UI
    window.location.href = '/dashboard';
  } else {
    showError('Authentication failed: ' + result.error);
  }
});
```

## Monitoring and Logging

### Security Event Logging

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "event_type": "fido2_authentication",
  "user_id": "<EMAIL>",
  "client_ip": "*************",
  "user_agent": "Mozilla/5.0...",
  "credential_id": "base64-encoded-id",
  "authenticator_data": {
    "rp_id_hash": "sha256-hash",
    "flags": {
      "user_present": true,
      "user_verified": true,
      "attested_credential_data": false,
      "extension_data": false
    },
    "sign_count": 42
  },
  "result": "success",
  "duration_ms": 1250
}
```

### Metrics to Monitor

- Authentication success/failure rates
- Device registration trends
- Browser compatibility issues
- Performance metrics (response times)
- Security events (suspicious activities)
- User adoption rates
- Device usage patterns

## Error Handling

### Common Error Codes

| Error Code | Description | Resolution |
|------------|-------------|------------|
| `NotSupportedError` | Browser doesn't support WebAuthn | Use fallback authentication |
| `SecurityError` | Invalid origin or HTTPS required | Check domain configuration |
| `NotAllowedError` | User cancelled or timeout | Retry with user guidance |
| `InvalidStateError` | Credential already exists | Use existing credential |
| `ConstraintError` | Unsupported algorithm | Update supported algorithms |
| `UnknownError` | Generic failure | Check device and retry |

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "NotAllowedError",
    "message": "User cancelled the operation",
    "details": "The user explicitly cancelled the WebAuthn operation",
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req-12345"
  }
}
```
